import { useEffect, useRef, useCallback } from 'react';
import type { DownloadItem, AppState } from '../../types/download';
import { ExtensionStatus } from '../../types/download';
import {
  getRequestIdFromUrl,
  generatePageTaskId,
  requestDownloadData,
  createMessageListener
} from '../../lib/download/messageUtils';

interface UseDownloaderInitProps {
  state: AppState;
  setPageTaskId: (pageTaskId: string) => void;
  setContentScriptReady: (ready: boolean) => void;
  setExtensionStatus: (status: ExtensionStatus) => void;
  setDownloadData: (data: DownloadItem | null) => void;
  setError: (error: string) => void;
  startDownloadWithData: (data: DownloadItem) => Promise<void>;
}

/**
 * 下载器初始化Hook
 * 负责页面初始化、消息监听和数据加载
 */
export function useDownloaderInit({
  state,
  setPageTaskId,
  setContentScriptReady,
  setExtensionStatus,
  setDownloadData,
  setError,
  startDownloadWithData
}: UseDownloaderInitProps) {
  const cleanupFunctions = useRef<Array<() => void>>([]);
  const startDownloadWithDataRef = useRef(startDownloadWithData);

  // 更新ref以获取最新的函数
  useEffect(() => {
    startDownloadWithDataRef.current = startDownloadWithData;
  }, [startDownloadWithData]);

  // 初始化页面任务ID
  useEffect(() => {
    const pageTaskId = generatePageTaskId();
    setPageTaskId(pageTaskId);
  }, [setPageTaskId]);

  // 设置消息监听器 - 需要尽早设置以避免错过插件消息
  useEffect(() => {
    console.log('🔧 设置消息监听器...');

    // 检查是否已经错过了插件消息 - 通过检查插件是否已经在页面中注入了全局标识
    const checkMissedMessage = () => {
      // 插件通常会在window对象上设置一些标识，或者我们可以主动询问
      console.log('🔍 检查是否错过了插件消息...');

      // 主动发送一个ping消息给插件，如果插件存在会回应
      window.postMessage({
        type: 'PING_EXTENSION',
        timestamp: Date.now()
      }, window.location.origin);
    };

    // 添加全局消息监听器用于调试
    const globalListener = (event: MessageEvent) => {
      if (event.source === window) {
        console.log('🌐 全局消息监听器收到消息:', event.data);
      }
    };
    window.addEventListener('message', globalListener);

    const cleanup1 = createMessageListener(async (event) => {
      const { type, data } = event.data;
      console.log('🔍 页面收到消息:', type, data);

      if (type === 'CONTENT_SCRIPT_READY') {
        console.log('✅ 内容脚本已准备就绪');
        setContentScriptReady(true);
      } else if (type === 'PONG_EXTENSION') {
        // 插件对ping的回应，说明插件存在
        console.log('✅ 收到插件ping回应，插件已安装');
        setContentScriptReady(true);
      } else if (type === 'HEADERS_SET_COMPLETED') {
        // 检查消息是否属于当前页面
        if (data && data.pageTaskId && data.pageTaskId === state.pageTaskId) {
          console.log('✅ 收到当前页面的请求头设置完成通知:', data);
          if (data.success) {
            console.log('✅ 请求头设置成功，可以开始下载');
          } else {
            console.error('❌ 请求头设置失败:', data.error);
          }
        } else {
          console.log('🚫 这是其他页面的请求头设置通知，当前页面忽略');
        }
      } else if (type === 'DOWNLOAD_DATA_RESPONSE' || type === 'SET_REQUEST_HEADERS_RESPONSE' ||
        type === 'DOWNLOAD_FILE_RESPONSE' || type === 'CLEANUP_REQUEST_HEADERS_RESPONSE') {
        // 这些是通过新的Chrome标准消息传递方式发送的响应
        // 检查消息是否属于当前页面
        if (data && data.pageTaskId && data.pageTaskId === state.pageTaskId) {
          console.log(`✅ 收到当前页面的${type}响应:`, data);
          // 触发相应的处理逻辑
          window.dispatchEvent(new CustomEvent(type, { detail: data }));
        } else {
          console.log(`🚫 忽略其他页面的${type}响应`);
        }
      }
    });

    cleanupFunctions.current.push(cleanup1);

    // 插件检测超时机制 - 2秒后如果没有收到插件响应，认为插件未安装
    const extensionDetectionTimer = setTimeout(() => {
      console.log('🔍 插件检测超时，未检测到SnapAny插件', state.extensionStatus, state.contentScriptReady);
      if (!state.contentScriptReady && state.extensionStatus === ExtensionStatus.DETECTING) {
        console.log('❌ 插件检测超时，未检测到SnapAny插件');
        setExtensionStatus(ExtensionStatus.NOT_INSTALLED);
      }
    }, 2000);

    return () => {
      clearTimeout(extensionDetectionTimer);
      window.removeEventListener('message', globalListener);
      cleanupFunctions.current.forEach(cleanup => cleanup());
      cleanupFunctions.current = [];
    };
  }, [state.pageTaskId, setContentScriptReady, setExtensionStatus, state.contentScriptReady, state.extensionStatus]);

  // 从URL加载下载数据
  const loadDownloadDataFromUrl = useCallback(() => {
    try {
      const requestId = getRequestIdFromUrl();

      if (requestId && state.pageTaskId) {
        console.log('从URL获取请求ID:', requestId, '页面任务ID:', state.pageTaskId);

        const cleanup = requestDownloadData(
          requestId,
          state.pageTaskId,
          (downloadData: DownloadItem) => {
            console.log('收到下载数据，准备开始下载:', downloadData);
            setDownloadData(downloadData);

            // 自动开始下载
            setTimeout(async () => {
              console.log('自动开始下载...');
              await startDownloadWithDataRef.current(downloadData);
            }, 100); // 稍微延迟确保状态更新完成
          },
          (error: string) => {
            setError(error);
          }
        );

        cleanupFunctions.current.push(cleanup);
      } else {
        console.warn('URL中没有找到请求ID');
        setError('没有找到请求ID');
      }
    } catch (error) {
      console.error('解析URL参数失败:', error);
      setError('解析URL参数失败: ' + (error as Error).message);
    }
  }, [state.pageTaskId, setDownloadData, setError]);

  // 当内容脚本和页面都准备就绪时，开始加载下载数据
  useEffect(() => {
    if (state.contentScriptReady && state.pageReady && state.pageTaskId) {
      console.log('内容脚本和页面都已准备就绪，开始加载下载数据');
      loadDownloadDataFromUrl();
    }
  }, [state.contentScriptReady, state.pageReady, state.pageTaskId, loadDownloadDataFromUrl]);

  return {
    cleanupFunctions
  };
}
