import type { PageMessage, DownloadItem, CustomEventDetail } from '../../types/download';

// 发送消息到内容脚本的函数
export function sendToContentScript(message: PageMessage): void {
  // 使用当前窗口的origin，避免消息发送到其他标签页
  window.postMessage(message, window.location.origin);
}

// 创建消息监听器
export function createMessageListener(
  onMessage: (event: MessageEvent) => void
): () => void {
  const listener = (event: MessageEvent) => {
    if (event.source !== window) return;
    onMessage(event);
  };

  window.addEventListener('message', listener);

  // 返回清理函数
  return () => {
    window.removeEventListener('message', listener);
  };
}

// 创建自定义事件监听器
export function createCustomEventListener(
  eventType: string,
  onEvent: (event: CustomEvent) => void
): () => void {
  const listener = (event: Event) => {
    onEvent(event as CustomEvent);
  };

  window.addEventListener(eventType, listener);

  // 返回清理函数
  return () => {
    window.removeEventListener(eventType, listener);
  };
}

// 触发自定义事件
export function dispatchCustomEvent(eventType: string, detail: CustomEventDetail): void {
  window.dispatchEvent(new CustomEvent(eventType, { detail }));
}

// 从URL参数中获取请求ID
export function getRequestIdFromUrl(): string | null {
  try {
    console.log('🔍 当前URL:', window.location.href);
    console.log('🔍 URL search参数:', window.location.search);
    const urlParams = new URLSearchParams(window.location.search);
    const requestId = urlParams.get('requestId');
    console.log('🔍 解析到的requestId:', requestId);
    console.log('🔍 所有URL参数:', Object.fromEntries(urlParams.entries()));
    return requestId;
  } catch (error) {
    console.error('解析URL参数失败:', error);
    return null;
  }
}

// 生成页面任务ID
export function generatePageTaskId(): string {
  return `page_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// 请求下载数据
export function requestDownloadData(
  requestId: string,
  _pageTaskId: string,
  onSuccess: (data: DownloadItem) => void,
  onError: (error: string) => void,
  timeout: number = 2000 // 默认2秒超时
): () => void {
  console.log('开始请求下载数据，requestId:', requestId);

  let isCompleted = false;

  // 设置超时处理
  const timeoutId = setTimeout(() => {
    if (!isCompleted) {
      isCompleted = true;
      console.error('请求下载数据超时');
      onError('请求下载数据超时，请检查扩展是否正确安装');
      cleanup();
    }
  }, timeout);

  // 监听来自内容脚本的响应
  const cleanup = createMessageListener((event) => {
    const { type, data } = event.data;
    console.log('收到消息:', type, data);

    // 收到任何消息都清除超时
    clearTimeout(timeoutId);

    if (type === 'DOWNLOAD_DATA_RESPONSE' && !isCompleted) {
      isCompleted = true;
      if (data.success) {
        // 确保下载数据包含requestId
        const downloadData = data.data;
        if (!downloadData.requestId) {
          downloadData.requestId = requestId; // 使用从URL获取的requestId
        }
        console.log('获取到下载数据:', downloadData);
        onSuccess(downloadData);
      } else {
        console.error('获取下载数据失败:', data.error);
        onError(data.error || '获取下载数据失败');
      }
      cleanup();
    }
  });

  console.log('向内容脚本发送请求...');
  // 向内容脚本发送请求
  sendToContentScript({
    type: 'GET_DOWNLOAD_DATA_BY_ID',
    data: { requestId }
  });

  // 返回清理函数
  return () => {
    clearTimeout(timeoutId);
    cleanup();
  };
}
